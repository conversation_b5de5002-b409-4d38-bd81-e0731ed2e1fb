import axios from 'axios';
import { Dispatch, SetStateAction } from 'react';
import { Platform } from 'react-native';
import RNFS from 'react-native-fs';
import SQLite from 'react-native-sqlite-storage';
import { API_BASE_URL, API_VERSION, CLIENT_ID, CLIENT_SECRET } from '@env';
import {
    AudioFile,
    insertAudioFile,
} from '../../services/audioFileManageService';

SQLite.DEBUG(false);
SQLite.enablePromise(true);

export type FileFormat = {
    uri: string;
    type: string;
    name: string;
};

const RequestService = (
    setAudioFiles: Dispatch<SetStateAction<AudioFile[]>>,
    isRequesting: boolean,
    setIsRequesting: Dispatch<SetStateAction<boolean>>,
) => {
    const handleSave = async (
        filePath: string,
        fileSvPath: string,
        taskId: string,
        status: string,
        result: string,
    ) => {
        const date = new Date();
        const day = date.getDate();
        const monthNames = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec',
        ];
        const month = monthNames[date.getMonth()];
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        const getOrdinalSuffix = (day: number) => {
            if (day > 3 && day < 21) return 'th';
            switch (day % 10) {
                case 1:
                    return 'st';
                case 2:
                    return 'nd';
                case 3:
                    return 'rd';
                default:
                    return 'th';
            }
        };

        const formattedDate = `${day}${getOrdinalSuffix(
            day,
        )} ${month}, ${year} ${hours}:${minutes}`;

        const db = await SQLite.openDatabase({
            name: 'audioFiles.db',
            location: 'default',
        });

        // Handle empty file paths
        let fileCsEcode = null;
        let fileSvEcode = null;

        if (filePath && filePath !== '') {
            try {
                fileCsEcode = await fileConverting(filePath);
            } catch (error) {
                console.error(`Error converting CS file: ${error}`);
            }
        }

        if (fileSvPath && fileSvPath !== '') {
            try {
                fileSvEcode = await fileConverting(fileSvPath);
            } catch (error) {
                console.error(`Error converting SV file: ${error}`);
            }
        }

        // Use null strings if conversion failed
        const csData = fileCsEcode ? JSON.stringify(fileCsEcode) : "null";
        const svData = fileSvEcode ? JSON.stringify(fileSvEcode) : "null";

        console.log(`Saving to database - CS: ${csData.substring(0, 50)}..., SV: ${svData.substring(0, 50)}...`);

        await insertAudioFile(
            db,
            csData,
            svData,
            formattedDate,
            status,
            result,
            taskId,
            setAudioFiles,
        );
    };

    const fileConverting = async (file: string) => {
        try {
            // Check if file exists
            const exists = await RNFS.exists(file);
            if (!exists) {
                console.error(`[Error fileConverting] File does not exist: ${file}`);
                return null;
            }

            // Get file stats
            const stats = await RNFS.stat(file);
            console.log(`File stats: ${JSON.stringify(stats)}`);

            // Extract filename and preserve it exactly as is
            const fileName = file.split('/').pop();
            console.log(`Converting file: ${fileName}`);

            // Read file content
            const fileContent = await RNFS.readFile(file, 'base64');
            console.log(`File content length: ${fileContent.length} characters`);

            // Create file object for FormData
            // For Android, we need to prefix the URI with 'file://'
            const fileUri = Platform.OS === 'android' ? `file://${file}` : file;

            // Determine if this is a CS or SV file based on the filename
            let isCs = false;
            let isSv = false;

            if (fileName) {
                isCs = fileName.toLowerCase().startsWith('cs');
                isSv = fileName.toLowerCase().startsWith('sv');
            }

            // Create the file data object with the original filename
            const fileData: FileFormat = {
                uri: fileUri,
                type: 'audio/wav',
                name: fileName || '',
            };

            console.log(`File converted successfully: ${fileName} (${isCs ? 'CS' : isSv ? 'SV' : 'Unknown'} type)`);
            return fileData;
        } catch (error) {
            console.error('[Error fileConverting]', error);
            return null;
        }
    };

    const handleSendRequest = async (
        fileCs: FileFormat,
        fileSv: FileFormat,
        token: string,
    ) => {
        try {
            console.log('Preparing to send files to backend:');
            console.log(`CS file: ${JSON.stringify(fileCs)}`);
            console.log(`SV file: ${JSON.stringify(fileSv)}`);

            // Create a new FormData instance
            const formData = new FormData();

            // Extract original filename from the URI
            const csFileName = fileCs.name || fileCs.uri.split('/').pop() || '';
            const svFileName = fileSv.name || fileSv.uri.split('/').pop() || '';

            // Ensure filenames follow the pattern cs{index}.wav and sv{index}.wav
            const timestamp = Date.now();
            const csRegex = /^cs\d+\.wav$/;
            const svRegex = /^sv\d+\.wav$/;

            // Use the original filename if it matches the pattern, otherwise use a timestamped name
            const finalCsName = csRegex.test(csFileName) ? csFileName : `cs_${timestamp}.wav`;
            const finalSvName = svRegex.test(svFileName) ? svFileName : `sv_${timestamp}.wav`;

            console.log(`Adding CS file to FormData with name: ${finalCsName}`);
            formData.append('files', {
                uri: fileCs.uri,
                type: 'audio/wav',
                name: finalCsName
            });

            console.log(`Adding SV file to FormData with name: ${finalSvName}`);
            formData.append('files', {
                uri: fileSv.uri,
                type: 'audio/wav',
                name: finalSvName
            });

            console.log('==============================================');
            console.log('UPLOADING FILES TO SERVER:');
            console.log(`CS File: ${finalCsName} (${fileCs.uri})`);
            console.log(`SV File: ${finalSvName} (${fileSv.uri})`);
            console.log('==============================================');

            console.log('FormData structure:');
            for (const [key, value] of Object.entries(formData)) {
                console.log(`${key}: ${JSON.stringify(value)}`);
            }

            console.log('FormData created with files');

            const apiUrl = `${API_BASE_URL}/api/${API_VERSION}/analyze`;

            console.log(`Sending request to: ${apiUrl}`);
            console.log(`Using token: ${token ? 'Token exists' : 'Token is undefined or empty'}`);

            // Get auth token if not provided
            let authToken = token;
            if (!authToken || authToken === 'undefined') {
                console.log('No token provided, using default credentials');
                // Try to get a token using client credentials
                try {
                    const basicAuth = `Basic ${Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64')}`;

                    console.log('Getting token from auth endpoint with client credentials...');
                    const authResponse = await axios.post(
                        `${API_BASE_URL}/api/${API_VERSION}/auth/token`,
                        {},
                        {
                            headers: {
                                'Authorization': basicAuth,
                                'Content-Type': 'application/json'
                            }
                        }
                    );

                    console.log('Auth response:', authResponse.data);

                    if (authResponse.data && authResponse.data.access_token) {
                        authToken = authResponse.data.access_token;
                        console.log('Successfully obtained token:', authToken);
                    } else {
                        console.log('Trying with stored credentials...');
                        authToken = 'b5a12d6e5478783864626843438278a5';
                        console.log('Using hardcoded token');
                    }
                } catch (authError) {
                    console.error('Error getting token:', authError);
                    authToken = 'b5a12d6e5478783864626843438278a5';
                    console.log('Using hardcoded token after error');
                }
            }

            const config = {
                method: 'POST',
                url: apiUrl,
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${authToken}`,
                },
                data: formData,
                timeout: 60000, // 60 seconds timeout
            };

            setIsRequesting(true);

            console.log('Sending request...');
            const response = await axios.request(config);
            console.log('Response received:', response.data);

            // Extract and log the task ID(s)
            if (response.data && response.data.tasks && Array.isArray(response.data.tasks)) {
                response.data.tasks.forEach((task: { task_id: string, file_cs: string, file_sv: string }, index: number) => {
                    console.log(`TASK ${index + 1} ID: ${task.task_id}`);
                    console.log(`TASK ${index + 1} FILES: CS=${task.file_cs}, SV=${task.file_sv}`);
                });
            } else if (response.data && response.data.task_id) {
                console.log(`TASK ID: ${response.data.task_id}`);
            }

            return response.data;
        } catch (error) {
            console.error('[Error sendFileToBackend]', JSON.stringify(error));
            if (axios.isAxiosError(error)) {
                console.error('Axios error details:', {
                    message: error.message,
                    code: error.code,
                    response: error.response?.data,
                    status: error.response?.status,
                });
            }
            setIsRequesting(false);
            return { code: 500, task_id: '' };
        }
    };

    const handleAnalysing = async (
        fileCs: string,
        fileCsTemp: string,
        fileSv: string,
        fileSvTemp: string,
        token: string,
    ) => {
        console.log('==============================================');
        console.log('STARTING ANALYSIS WITH FILES:');
        console.log(`CS file: ${fileCs}`);
        console.log(`SV file: ${fileSv}`);
        console.log('==============================================');

        // Check if files exist
        if (!fileCs || !fileSv || fileCs === '' || fileSv === '') {
            console.error('Missing file paths');
            return 'failed';
        }

        let csFilePath = fileCs;
        let svFilePath = fileSv;

        if (Platform.OS === 'ios') {
            console.log('iOS: Checking file paths');

            if (csFilePath.startsWith('file://')) {
                csFilePath = csFilePath.replace('file://', '');
                console.log(`iOS: Adjusted CS path: ${csFilePath}`);
            }

            if (svFilePath.startsWith('file://')) {
                svFilePath = svFilePath.replace('file://', '');
                console.log(`iOS: Adjusted SV path: ${svFilePath}`);
            }
        }

        const csFExists = await RNFS.exists(csFilePath);
        const svFExists = await RNFS.exists(svFilePath);

        if (!csFExists || !svFExists) {
            console.error(`Files don't exist on disk. CS: ${csFExists}, SV: ${svFExists}`);
            return 'failed';
        }

        console.log('Files exist on disk, proceeding with conversion');

        try {
            // Convert files to the format needed for the API
            const fileCsData = await fileConverting(fileCs);
            const fileSvData = await fileConverting(fileSv);

            // Check if conversion was successful
            if (!fileCsData || !fileSvData) {
                console.error('File conversion failed');
                await handleSave('', '', '', 'Failed', '');
                return 'failed';
            }

            console.log('Files converted successfully, sending to backend');

            // Send files to backend
            const result = await handleSendRequest(fileCsData, fileSvData, token);

            if (result) {
                console.log('Received result from backend:', result);

                if (result.code === 200 || result.code === 202) {
                    // Success - save the task ID
                    const taskId = result.tasks && result.tasks[0] ? result.tasks[0].task_id : result.task_id;

                    // Log the task ID and filenames prominently for easy reference
                    console.log('==============================================');
                    console.log(`ANALYSIS TASK ID: ${taskId}`);
                    if (result.tasks && result.tasks[0]) {
                        console.log(`FILES: CS=${result.tasks[0].file_cs}, SV=${result.tasks[0].file_sv}`);
                    }
                    console.log('==============================================');

                    console.log(`Analysis request successful, task ID: ${taskId}`);

                    await handleSave(
                        fileCsTemp,
                        fileSvTemp,
                        taskId,
                        'Pending',
                        '',
                    );
                    return 'done';
                } else {
                    // Error
                    console.error('Backend returned error:', result);
                    await handleSave('', '', result.task_id || '', 'Failed', '');
                    return 'failed';
                }
            } else {
                console.error('No result from backend');
                await handleSave('', '', '', 'Failed', '');
                return 'failed';
            }
        } catch (error) {
            console.error('[Error handleAnalysing]', error);
            await handleSave('', '', '', 'Failed', '');
            return 'failed';
        } finally {
            setIsRequesting(false);
        }
    };

    return {
        handleAnalysing,
        handleSave,
        fileConverting,
        handleSendRequest,
    };
};

export const decodeAudioFile = async (base64: string, name: string) => {
    try {
        // Check if base64 is null or empty
        if (!base64) {
            console.log('[decodeAudioFile] base64 data is null or empty, skipping file write');
            return '';
        }

        // Create a directory in external storage for better visibility
        const directory = `${RNFS.ExternalStorageDirectoryPath}/Download/VoiceBack/`;

        try {
            // Make sure the directory exists
            await RNFS.mkdir(directory);
        } catch (dirError) {
            console.error(`[Error creating directory] ${dirError}`);

            // Check if we can use internal storage
            try {
                // Fallback to internal storage
                const newFile = `${RNFS.DocumentDirectoryPath}/file_${name}.wav`;

                // Double-check that base64 is valid before writing
                if (typeof base64 !== 'string') {
                    console.error('[Error decodeAudioFile] base64 is not a string:', typeof base64);
                    return '';
                }

                await RNFS.writeFile(newFile, base64, 'base64');
                console.log('File saved to internal storage:', newFile);
                return newFile;
            } catch (writeError) {
                console.error('[Error writing to internal storage]', writeError);
                return '';
            }
        }

        try {
            // Save to external storage
            const newFile = `${directory}file_${name}.wav`;

            // Double-check that base64 is valid before writing
            if (typeof base64 !== 'string') {
                console.error('[Error decodeAudioFile] base64 is not a string:', typeof base64);
                return '';
            }

            await RNFS.writeFile(newFile, base64, 'base64');
            console.log('File saved to external storage:', newFile);
            return newFile;
        } catch (writeError) {
            console.error('[Error writing to external storage]', writeError);
            return '';
        }
    } catch (error) {
        console.error('[Error decodeAudioFile]', error);
        return '';
    }
};

export default RequestService;